import React from 'react';
import { useTranslation } from "react-i18next";

function AboutUs() {
    const { t } = useTranslation();

    return (
        <div className="min-h-screen bg-gradient-to-r from-indigo-500 via-purple-500 to-indigo-400 text-white">
            <div className="container mx-auto px-4 py-16">
                <h1 className="text-4xl md:text-5xl mb-16 font-bold text-center">{t('about.title') || 'О нас'}</h1>
                
                {/* Main content sections with wavy dividers */}
                <div className="max-w-4xl mx-auto space-y-12">
                    {/* First section */}
                    <div className="relative">
                        <div className="absolute -top-6 left-0 right-0">
                            <svg viewBox="0 0 1200 120" preserveAspectRatio="none" className="w-full h-6">
                                <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" 
                                    fill="#f0f0f0" fillOpacity=".95"></path>
                            </svg>
                        </div>
                        <div className="bg-white rounded-xl p-8 shadow-lg">
                            <div className="flex flex-col md:flex-row items-center gap-8">
                                <div className="md:w-1/3">
                                    <div className="flex justify-center">
                                        <img src="/assets/about-icon1.svg" alt="About icon" className="w-32 h-32" />
                                    </div>
                                </div>
                                <div className="md:w-2/3">
                                    <h2 className="text-2xl font-bold text-gray-800 mb-4">{t('about.section1.title') || 'О нас'}</h2>
                                    <div className="text-gray-700">
                                        <p className="mb-3" dangerouslySetInnerHTML={{ __html: t('about.description.p1') }} />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {/* Second section */}
                    <div className="relative">
                        <div className="absolute -top-6 left-0 right-0">
                            <svg viewBox="0 0 1200 120" preserveAspectRatio="none" className="w-full h-6">
                                <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" 
                                    fill="#f8e0ff" fillOpacity=".95"></path>
                            </svg>
                        </div>
                        <div className="bg-white rounded-xl p-8 shadow-lg">
                            <div className="flex flex-col md:flex-row items-center gap-8">
                                <div className="md:w-1/3">
                                    <div className="flex justify-center">
                                        <img src="/assets/lock-icon.svg" alt="Security icon" className="w-32 h-32" />
                                    </div>
                                </div>
                                <div className="md:w-2/3">
                                    <h2 className="text-2xl font-bold text-gray-800 mb-4">{t('about.section2.title') || 'В чем наша безопасность?'}</h2>
                                    <div className="text-gray-700">
                                        <p className="mb-3">{t('about.description.p2')}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {/* Third section */}
                    <div className="relative">
                        <div className="absolute -top-6 left-0 right-0">
                            <svg viewBox="0 0 1200 120" preserveAspectRatio="none" className="w-full h-6">
                                <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" 
                                    fill="#e0e8ff" fillOpacity=".95"></path>
                            </svg>
                        </div>
                        <div className="bg-white rounded-xl p-8 shadow-lg">
                            <div className="flex flex-col md:flex-row items-center gap-8">
                                <div className="md:w-1/3">
                                    <div className="flex justify-center">
                                        <img src="/assets/buildings-icon.svg" alt="Buildings icon" className="w-32 h-32" />
                                    </div>
                                </div>
                                <div className="md:w-2/3">
                                    <h2 className="text-2xl font-bold text-gray-800 mb-4">{t('about.section3.title') || 'Для кого создан TOGOLOCK?'}</h2>
                                    <div className="text-gray-700">
                                        <p className="mb-3">{t('about.description.p3')}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {/* Advantages section */}
                <div className="mt-20 max-w-4xl mx-auto">
                    <h2 className="text-3xl font-bold text-center mb-10">{t('about.advantagesTitle') || 'Преимущества'}</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        {[1, 2, 3, 4].map(num => (
                            <div key={num} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                                <p className="text-sm font-semibold text-white/80">0{num}</p>
                                <hr className="my-2 border-white/30" />
                                <p className="font-semibold text-xl mb-2">{t(`about.advantages.0${num}.title`)}</p>
                                <p className="text-white/80">
                                    {t(`about.advantages.0${num}.text`)}
                                </p>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default AboutUs;
